const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  const whereClause = {
    deletedAt: null, // Exclude soft-deleted records
  };

  // Add additional filters if provided
  if (params?.status) {
    whereClause.status = params.status;
  }
  if (params?.customerId) {
    whereClause.customerId = params.customerId;
  }
  if (params?.isPrimary !== undefined) {
    whereClause.isPrimary = params.isPrimary;
  }
  if (params?.designation) {
    whereClause.designation = params.designation;
  }
  if (params?.department) {
    whereClause.department = params.department;
  }

  return await db.CustomerEmployee.findAll({
    where: whereClause,
    include: [
      {
        model: db.Customer,
        as: "customerInfo",
        attributes: ["id", "name", "email"],
      },
    ],
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Set default status if not provided
  if (!params.status) params.status = "active";
  // Set audit fields
  params.createdBy = params.userId || null;

  const record = await db.CustomerEmployee.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Set audit fields
  params.updatedBy = params.userId || null;

  // copy params to CustomerEmployee and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Soft delete
  record.deletedBy = userId || null;
  record.deletedAt = new Date();
  await record.save();

  // Or hard delete if preferred
  // await record.destroy();
}

async function getSingleRecord(id) {
  const record = await db.CustomerEmployee.findOne({
    where: { id, deletedAt: null },
    include: [
      {
        model: db.Customer,
        as: "customerInfo",
        attributes: ["id", "name", "email"],
      },
    ],
  });

  if (!record) throw "Customer Employee not found";
  return record;
}

async function bulkImport(filePath, userId) {
  try {
    // Read the Excel file
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(worksheet);

    // Clean up the uploaded file
    fs.unlinkSync(filePath);

    if (!jsonData || jsonData.length === 0) {
      throw new Error("No data found in the uploaded file");
    }

    const totalRows = jsonData.length;
    let insertedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    const insertedData = [];
    const skippedData = [];
    const errors = [];

    // Get all existing customer employees to check for duplicates
    const existingEmployees = await db.CustomerEmployee.findAll({
      where: { deletedAt: null },
      attributes: ["email", "customerId"],
    });

    // Create a map for quick lookup of existing employees by email+customerId
    const existingEmployeeMap = new Map();
    existingEmployees.forEach((emp) => {
      if (emp.email) {
        const key = `${emp.email.toLowerCase()}-${emp.customerId}`;
        existingEmployeeMap.set(key, emp);
      }
    });

    // Get all customers for foreign key validation
    const customers = await db.Customer.findAll({
      where: { deletedAt: null },
      attributes: ["id", "name", "slug"],
    });

    // Create a map for customer lookup by slug
    const customerSlugMap = new Map();
    customers.forEach((customer) => {
      customerSlugMap.set(customer.slug, customer);
    });

    const insertList = [];

    jsonData.forEach((row, index) => {
      try {
        const name = row.name?.toString().trim();
        const email = row.email?.toString().trim();
        const customerSlug = row.customerSlug?.toString().trim();

        // Validate required fields
        if (!name) {
          errors.push({
            row: index + 1,
            error: "Name is required",
            data: row,
          });
          errorCount++;
          return;
        }

        if (!customerSlug) {
          errors.push({
            row: index + 1,
            error: "Customer slug is required",
            data: row,
          });
          errorCount++;
          return;
        }

        // Check if customer exists
        if (!customerSlugMap.has(customerSlug)) {
          skippedData.push({
            row: index + 1,
            reason: "Customer not found",
            data: row,
          });
          skippedCount++;
          return;
        }

        // Check for duplicates (email + customerId combination)
        if (email) {
          const customerId = customerSlugMap.get(customerSlug).id;
          const duplicateKey = `${email.toLowerCase()}-${customerId}`;
          if (existingEmployeeMap.has(duplicateKey)) {
            skippedData.push({
              row: index + 1,
              reason: "Duplicate employee (same email for this customer)",
              data: row,
            });
            skippedCount++;
            return;
          }

          // Add to existing map to prevent duplicates within the same file
          existingEmployeeMap.set(duplicateKey, { email, customerId });
        }

        // Get customerId from the customer lookup
        const customerId = customerSlugMap.get(customerSlug).id;

        // Prepare employee data for insertion
        const employeeData = {
          name: name,
          email: email || null,
          phone: row.phone?.toString().trim() || null,
          designation: row.designation?.toString().trim() || null,
          department: row.department?.toString().trim() || null,
          isPrimary:
            row.isPrimary === "true" || row.isPrimary === true || false,
          customerId: customerId,
          status: "active",
          notes: row.notes?.toString().trim() || null,
          createdBy: userId || null,
        };

        insertList.push(employeeData);
      } catch (error) {
        errors.push({
          row: index + 1,
          error: error.message,
          data: row,
        });
        errorCount++;
      }
    });

    // Bulk insert the valid records
    if (insertList.length > 0) {
      const insertedRecords = await db.CustomerEmployee.bulkCreate(insertList, {
        returning: true,
      });
      insertedCount = insertedRecords.length;
      insertedData.push(...insertedRecords);
    }

    return {
      totalRows,
      insertedCount,
      skippedCount,
      errorCount,
      insertedData,
      skippedData,
      errors,
    };
  } catch (error) {
    // Clean up the uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}
