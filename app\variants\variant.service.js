const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  const whereClause = {
    deletedAt: null, // Exclude soft-deleted records
  };

  // Add additional filters if provided
  if (params?.status) {
    whereClause.status = params.status;
  }
  if (params?.propertyId) {
    whereClause.propertyId = params.propertyId;
  }

  return await db.Variant.findAll({
    where: whereClause,
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Generate slug from name
  params.slug = utils.generateSlug(params.name);
  // Generate code from name
  params.code = utils.generateSlug(params.name);
  // Set default status if not provided
  if (!params.status) params.status = "active";
  // Set audit fields
  params.createdBy = params.userId || null;

  const record = await db.Variant.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Update slug and code if name is changed
  if (params.name && params.name !== record.name) {
    params.slug = utils.generateSlug(params.name);
    params.code = utils.generateSlug(params.name);
  }

  // Set audit fields
  params.updatedBy = params.userId || null;

  // copy params to Variant and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Soft delete
  record.deletedBy = userId || null;
  record.deletedAt = new Date();
  await record.save();

  // Or hard delete if preferred
  // await record.destroy();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect variant names from Excel and generate slugs
    const variantNamesInSheet = rows
      .map(
        (row) =>
          row.Name ||
          row.name ||
          row.NAME ||
          row.VariantName ||
          row["Variant Name"] ||
          row.variant_name ||
          row.VARIANT_NAME
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const slugsInSheet = variantNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Get existing variants with matching slugs
    const existingVariants = await db.Variant.findAll({
      where: {
        slug: slugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "name", "slug"],
    });

    // Create a map for quick lookup of existing variants
    const existingSlugMap = new Map();
    existingVariants.forEach((variant) => {
      existingSlugMap.set(variant.slug, variant);
    });

    // Collect property names from Excel for foreign key lookup
    const propertyNamesInSheet = rows
      .map(
        (row) =>
          row.Property ||
          row.property ||
          row.PROPERTY ||
          row.PropertyName ||
          row["Property Name"] ||
          row.property_name ||
          row.PROPERTY_NAME
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const propertySlugsInSheet = propertyNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Get existing properties for foreign key validation
    const existingProperties = await db.Property.findAll({
      where: {
        slug: propertySlugsInSheet,
        deletedAt: null,
      },
      attributes: ["id", "name", "slug"],
    });

    // Create a map for quick lookup of properties
    const propertySlugMap = new Map();
    existingProperties.forEach((property) => {
      propertySlugMap.set(property.slug, property);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    rows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)

      try {
        // Extract variant data with multiple column name variations
        const name = (
          row.Name ||
          row.name ||
          row.NAME ||
          row.VariantName ||
          row["Variant Name"] ||
          row.variant_name ||
          row.VARIANT_NAME
        )
          ?.toString()
          .trim();

        const propertyName = (
          row.Property ||
          row.property ||
          row.PROPERTY ||
          row.PropertyName ||
          row["Property Name"] ||
          row.property_name ||
          row.PROPERTY_NAME
        )
          ?.toString()
          .trim();

        // Validate required fields
        if (!name) {
          errorLog.push({
            row: rowNumber,
            reason: "Variant name is required",
            data: row,
          });
          return;
        }

        if (!propertyName) {
          errorLog.push({
            row: rowNumber,
            reason: "Property name is required",
            data: row,
          });
          return;
        }

        const slug = utils.generateSlug(name);

        // Check if variant already exists
        if (existingSlugMap.has(slug)) {
          skippedList.push({
            row: rowNumber,
            reason: "Variant already exists",
            data: row,
          });
          return;
        }

        // Validate property exists (foreign key validation)
        const propertySlug = utils.generateSlug(propertyName);
        if (!propertySlugMap.has(propertySlug)) {
          errorLog.push({
            row: rowNumber,
            reason: `Property "${propertyName}" not found`,
            data: row,
          });
          return;
        }

        const propertyId = propertySlugMap.get(propertySlug).id;

        // Extract other optional fields
        const description =
          (
            row.Description ||
            row.description ||
            row.DESCRIPTION ||
            row.Desc ||
            row.desc ||
            row.DESC
          )
            ?.toString()
            .trim() || null;

        const status =
          (row.Status || row.status || row.STATUS)
            ?.toString()
            .trim()
            .toLowerCase() || "active";

        // Validate status
        if (!["active", "inactive"].includes(status)) {
          errorLog.push({
            row: rowNumber,
            reason: "Status must be one of: active, inactive",
            data: row,
          });
          return;
        }

        // Prepare variant data for insertion
        const variantData = {
          name: name,
          slug: slug,
          code: slug,
          propertyId: propertyId,
          description: description,
          status: status,
          createdBy: userId || null,
        };

        insertList.push(variantData);
        // Add to existing map to prevent duplicates within the same file
        existingSlugMap.set(slug, { name: name, slug: slug });
      } catch (error) {
        errorLog.push({
          row: rowNumber,
          reason: `Processing error: ${error.message}`,
          data: row,
        });
      }
    });

    // Perform bulk insert using transaction
    let insertedVariants = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedVariants = await db.Variant.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return standardized results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedVariants.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedVariants,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    // Create a map of rejected rows by row number for quick lookup
    const rejectedRowsMap = new Map();
    rejectedRows.forEach((rejected) => {
      rejectedRowsMap.set(rejected.row, rejected.reason);
    });

    // Filter original rows to get only rejected ones and add reason column
    const rejectedRowsWithReasons = [];

    originalRows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)
      if (rejectedRowsMap.has(rowNumber)) {
        // Add the reason column to the original row data
        const rowWithReason = {
          ...row,
          "Rejection Reason": rejectedRowsMap.get(rowNumber),
        };
        rejectedRowsWithReasons.push(rowWithReason);
      }
    });

    if (rejectedRowsWithReasons.length === 0) {
      return null;
    }

    // Create new workbook with rejected rows
    const newWorkbook = xlsx.utils.book_new();
    const newWorksheet = xlsx.utils.json_to_sheet(rejectedRowsWithReasons);
    xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, "Rejected Rows");

    // Generate filename for rejected rows file
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Write the rejected rows file
    xlsx.writeFile(newWorkbook, rejectedFilePath);

    // Return the download URL (relative to uploads directory)
    const uploadsDir = path.join(__basedir, "uploads");
    const relativePath = path.relative(uploadsDir, rejectedFilePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Variant.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
