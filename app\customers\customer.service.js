const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");
const { Op, Sequelize } = require("sequelize");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
};

async function getAll(params) {
  const whereClause = {
    deletedAt: null, // Exclude soft-deleted records
  };

  // Add additional filters if provided
  if (params?.status) {
    whereClause.status = params.status;
  }
  if (params?.industry) {
    whereClause.industry = params.industry;
  }
  if (params?.companyCategoryId) {
    whereClause.companyCategoryId = params.companyCategoryId;
  }
  if (params?.cityId) {
    whereClause.cityId = params.cityId;
  }

  return await db.Customer.findAll({
    where: whereClause,
    include: [
      {
        model: db.City,
        as: "cityInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.CompanyCategory,
        as: "companyCategoryInfo",
        attributes: ["id", "name"],
      },
    ],
    order: [["name", "ASC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Generate slug from name if not provided
  if (!params.slug && params.name) {
    params.slug = utils.generateSlug(params.name);
  }

  // Generate code if not provided
  if (!params.code && params.name) {
    params.code = utils.generateSlug(params.name);
  }

  const record = await db.Customer.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Add userId for audit tracking
  if (params.userId) {
    params.updatedBy = params.userId;
    delete params.userId; // Remove userId from params as it's not a model field
  }

  // copy params to Customer and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id, userId) {
  const record = await getSingleRecord(id);

  // Perform soft delete
  record.deletedAt = new Date();
  if (userId) {
    record.deletedBy = userId;
  }
  await record.save();
}

async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Collect customer names from Excel and generate slugs
    const customerNamesInSheet = rows
      .map(
        (row) =>
          row.Name ||
          row.name ||
          row.CompanyName ||
          row["Company Name"] ||
          row.CustomerName ||
          row["Customer Name"]
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const slugsInSheet = customerNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Get existing customers by slug to prevent duplicates
    const existingCustomers = await db.Customer.findAll({
      where: {
        slug: { [Op.in]: slugsInSheet },
        deletedAt: null,
      },
      attributes: ["id", "name", "slug"],
    });

    // Create a map for quick lookup
    const existingSlugMap = new Map();
    existingCustomers.forEach((customer) => {
      existingSlugMap.set(customer.slug, {
        id: customer.id,
        name: customer.name,
        slug: customer.slug,
      });
    });

    // Collect city names for foreign key validation
    const cityNamesInSheet = rows
      .map((row) => row.City || row.city || row.CityName || row["City Name"])
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const citySlugsInSheet = cityNamesInSheet.map((name) =>
      utils.generateSlug(name)
    );

    // Get existing cities by slug for foreign key validation
    const existingCities = await db.City.findAll({
      where: {
        slug: { [Op.in]: citySlugsInSheet },
        deletedAt: null,
      },
      attributes: ["id", "name", "slug"],
    });

    const citySlugToIdMap = new Map();
    existingCities.forEach((city) => {
      citySlugToIdMap.set(city.slug, city.id);
    });

    // Collect company category names for foreign key validation
    const companyCategoryNamesInSheet = rows
      .map(
        (row) =>
          row.CompanyCategory ||
          row["Company Category"] ||
          row.companyCategory ||
          row.company_category ||
          row.Category ||
          row.category
      )
      .filter(Boolean)
      .map((name) => name.toString().trim());

    const companyCategorySlugsInSheet = companyCategoryNamesInSheet.map(
      (name) => utils.generateSlug(name)
    );

    // Get existing company categories by slug for foreign key validation
    const existingCompanyCategories = await db.CompanyCategory.findAll({
      where: {
        slug: { [Op.in]: companyCategorySlugsInSheet },
        deletedAt: null,
      },
      attributes: ["id", "name", "slug"],
    });

    const companyCategorySlugToIdMap = new Map();
    existingCompanyCategories.forEach((category) => {
      companyCategorySlugToIdMap.set(category.slug, category.id);
    });

    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Process each row
    rows.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-indexed + header)

      try {
        // Extract and validate required fields
        const name = (
          row.Name ||
          row.name ||
          row.CompanyName ||
          row["Company Name"] ||
          row.CustomerName ||
          row["Customer Name"] ||
          ""
        )
          .toString()
          .trim();

        if (!name) {
          errorLog.push({
            row: rowNumber,
            data: row,
            reason: "Missing required field: Name/Company Name",
          });
          return;
        }

        const slug = utils.generateSlug(name);

        // Check for duplicates
        if (existingSlugMap.has(slug)) {
          skippedList.push({
            row: rowNumber,
            data: row,
            reason: `Duplicate: Customer '${name}' already exists`,
          });
          return;
        }

        // Extract optional fields with comprehensive column name mapping
        const email = (row.Email || row.email || "").toString().trim() || null;
        const phone =
          (row.Phone || row.phone || row.Mobile || row.mobile || "")
            .toString()
            .trim() || null;
        const address =
          (row.Address || row.address || "").toString().trim() || null;
        const state = (row.State || row.state || "").toString().trim() || null;
        const country = (row.Country || row.country || "India")
          .toString()
          .trim();
        const pincode =
          (
            row.Pincode ||
            row.pincode ||
            row.PIN ||
            row.pin ||
            row.Postal ||
            row.postal ||
            ""
          )
            .toString()
            .trim() || null;
        const website =
          (row.Website || row.website || row.URL || row.url || "")
            .toString()
            .trim() || null;

        const industry =
          (row.Industry || row.industry || row.Sector || row.sector || "")
            .toString()
            .trim() || null;
        const gstNumber =
          (
            row.GST ||
            row.gst ||
            row.GSTNumber ||
            row["GST Number"] ||
            row.GSTIN ||
            row.gstin ||
            ""
          )
            .toString()
            .trim() || null;
        const panNumber =
          (row.PAN || row.pan || row.PANNumber || row["PAN Number"] || "")
            .toString()
            .trim() || null;
        const logo =
          (
            row.Logo ||
            row.logo ||
            row.LogoURL ||
            row["Logo URL"] ||
            row.Image ||
            row.image ||
            ""
          )
            .toString()
            .trim() || null;
        const notes =
          (
            row.Notes ||
            row.notes ||
            row.Description ||
            row.description ||
            row.Comments ||
            row.comments ||
            ""
          )
            .toString()
            .trim() || null;
        const status = (row.Status || row.status || "active")
          .toString()
          .toLowerCase();

        // Validate status
        if (!["active", "inactive", "lead"].includes(status)) {
          errorLog.push({
            row: rowNumber,
            data: row,
            reason: `Invalid status: '${status}'. Must be 'active', 'inactive', or 'lead'`,
          });
          return;
        }

        // Handle city foreign key
        let cityId = null;
        const cityName = (
          row.City ||
          row.city ||
          row.CityName ||
          row["City Name"] ||
          ""
        )
          .toString()
          .trim();
        if (cityName) {
          const citySlug = utils.generateSlug(cityName);
          cityId = citySlugToIdMap.get(citySlug);
          if (!cityId) {
            // Skip this record if city is not found
            skippedList.push({
              row: rowNumber,
              data: row,
              reason: `City '${cityName}' not found in database`,
            });
            return;
          }
        }

        // Handle company category foreign key
        let companyCategoryId = null;
        const companyCategoryName = (
          row.CompanyCategory ||
          row["Company Category"] ||
          row.companyCategory ||
          row.company_category ||
          row.Category ||
          row.category ||
          ""
        )
          .toString()
          .trim();
        if (companyCategoryName) {
          const companyCategorySlug = utils.generateSlug(companyCategoryName);
          companyCategoryId =
            companyCategorySlugToIdMap.get(companyCategorySlug);
          if (!companyCategoryId) {
            // Skip this record if company category is not found
            skippedList.push({
              row: rowNumber,
              data: row,
              reason: `Company Category '${companyCategoryName}' not found in database`,
            });
            return;
          }
        }

        // Prepare customer data for insertion
        const customerData = {
          name: name,
          slug: slug,
          code: slug, // Use slug as code
          email: email,
          phone: phone,
          address: address,
          cityId: cityId,
          state: state,
          country: country,
          pincode: pincode,
          website: website,
          companyCategoryId: companyCategoryId,
          industry: industry,
          gstNumber: gstNumber,
          panNumber: panNumber,
          logo: logo,
          status: status,
          notes: notes,
          createdBy: userId || null,
        };

        insertList.push(customerData);
        // Add to existing map to prevent duplicates within the same file
        existingSlugMap.set(slug, { name: name, slug: slug });
      } catch (error) {
        errorLog.push({
          row: rowNumber,
          data: row,
          reason: `Processing error: ${error.message}`,
        });
      }
    });

    // Perform bulk insert using transaction
    let insertedCustomers = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedCustomers = await db.Customer.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return standardized results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedCustomers.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedCustomers,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

// Helper function to create rejected rows file
async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    const rejectedData = [];

    // Add header row with reason column
    const headerRow = Object.keys(originalRows[0] || {});
    headerRow.push("Rejection Reason");
    rejectedData.push(headerRow);

    // Add rejected rows with reasons
    rejectedRows.forEach((rejected) => {
      const rowData = Object.values(rejected.data);
      rowData.push(rejected.reason);
      rejectedData.push(rowData);
    });

    // Create new workbook with rejected data
    const newWorkbook = xlsx.utils.book_new();
    const newWorksheet = xlsx.utils.aoa_to_sheet(rejectedData);
    xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, "Rejected Rows");

    // Generate unique filename for rejected file
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Write the rejected file
    xlsx.writeFile(newWorkbook, rejectedFilePath);

    // Return URL for download (adjust based on your file serving setup)
    return `/uploads/customers/${path.basename(rejectedFilePath)}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// helper function
async function getSingleRecord(id) {
  const record = await db.Customer.findOne({
    where: { id, deletedAt: null },
    include: [
      {
        model: db.City,
        as: "cityInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.CompanyCategory,
        as: "companyCategoryInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!record) throw "Customer not found";
  return record;
}
