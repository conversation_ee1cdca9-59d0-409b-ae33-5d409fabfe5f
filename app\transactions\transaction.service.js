const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { Op, Sequelize } = require("sequelize");
const xlsx = require("xlsx");
const fs = require("fs");
const path = require("path");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  bulkImport,
  // Dashboard Analytics APIs
  getDashboardSummary,
  getRevenueOverTime,
  getYearOverYearComparison,
  getRevenueByCategory,
  getTopClients,
  getFilterOptions,
  // Consolidated Dashboard API
  getAllDashboardData,
  // Reports API
  generateTransactionReport,
  generateStageCustomerReport,
};

async function getAll(params) {
  const whereClause = {
    deletedAt: null, // Only get non-deleted records
  };

  // Add any additional filtering based on params
  if (params.status) {
    whereClause.status = params.status;
  }
  if (params.paymentStatus) {
    whereClause.paymentStatus = params.paymentStatus;
  }
  if (params.customerId) {
    whereClause.customerId = params.customerId;
  }

  return await db.Transaction.findAll({
    where: whereClause,
    include: [
      {
        model: db.Customer,
        as: "customerInfo",
        attributes: ["id", "name", "code", "email"],
        required: false,
      },
      {
        model: db.Medium,
        as: "mediumInfo",
        attributes: ["id", "name", "code"],
        required: false,
      },
      {
        model: db.Channel,
        as: "channelInfo",
        attributes: ["id", "name", "code"],
        required: false,
      },
      {
        model: db.Property,
        as: "propertyInfo",
        attributes: ["id", "name", "code"],
        required: false,
      },
      {
        model: db.Variant,
        as: "variantInfo",
        attributes: ["id", "name", "code"],
        required: false,
      },
      {
        model: db.City,
        as: "cityInfo",
        attributes: ["id", "name", "code", "state"],
        required: false,
      },
    ],
    order: [["createdAt", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecordWithDetails(id);
}

async function create(params) {
  // Set default values and audit fields
  const transactionData = {
    ...params,
    status: params.status || "active",
    paymentStatus: params.paymentStatus || "pending",
    createdBy: params.createdBy || params.userId || null,
  };

  // Remove userId from params as it's not a model field
  if (transactionData.userId) {
    delete transactionData.userId;
  }

  const record = await db.Transaction.create(transactionData);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // Add audit field for updates
  const updateData = {
    ...params,
    updatedBy: params.updatedBy || null,
  };

  // copy params to Transaction and save
  Object.assign(record, updateData);
  await record.save();

  return record.get();
}

async function _delete(id, deletedBy = null) {
  const record = await getSingleRecord(id);

  // Implement soft delete
  record.deletedAt = new Date();
  record.deletedBy = deletedBy;
  record.status = "cancelled";

  await record.save();

  return { message: "Transaction soft deleted successfully" };
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Transaction.findOne({
    where: {
      id: id,
      deletedAt: null, // Only get non-deleted records
    },
  });
  if (!record) throw "Record not found";
  return record;
}

async function getSingleRecordWithDetails(id) {
  const record = await db.Transaction.findOne({
    where: {
      id: id,
      deletedAt: null, // Only get non-deleted records
    },
    include: [
      {
        model: db.Customer,
        as: "customerInfo",
        attributes: [
          "id",
          "name",
          "code",
          "email",
          "phone",
          "address",
          "gstNumber",
        ],
        required: false,
      },
      {
        model: db.CustomerEmployee,
        as: "customerEmployeeInfo",
        attributes: [
          "id",
          "name",
          "email",
          "phone",
          "designation",
          "department",
        ],
        required: false,
      },
      {
        model: db.Medium,
        as: "mediumInfo",
        attributes: ["id", "name", "code", "description"],
        required: false,
      },
      {
        model: db.Channel,
        as: "channelInfo",
        attributes: ["id", "name", "code", "description"],
        required: false,
      },
      {
        model: db.Property,
        as: "propertyInfo",
        attributes: ["id", "name", "code", "description"],
        required: false,
      },
      {
        model: db.Variant,
        as: "variantInfo",
        attributes: ["id", "name", "code", "description"],
        required: false,
      },
      {
        model: db.City,
        as: "cityInfo",
        attributes: ["id", "name", "code", "state", "country"],
        required: false,
      },
    ],
  });
  if (!record) throw "Record not found";
  return record;
}

// Bulk Import Function
async function bulkImport(filePath, userId) {
  try {
    // Load Excel file
    const workbook = xlsx.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows || rows.length === 0) {
      throw new Error("Excel file is empty or invalid");
    }

    // Initialize tracking arrays
    const insertList = [];
    const skippedList = [];
    const errorLog = [];

    // Month mapping from short forms to full names
    const monthMapping = {
      jan: "January",
      feb: "February",
      mar: "March",
      apr: "April",
      may: "May",
      jun: "June",
      jul: "July",
      aug: "August",
      sep: "September",
      oct: "October",
      nov: "November",
      dec: "December",
      january: "January",
      february: "February",
      march: "March",
      april: "April",
      june: "June",
      july: "July",
      august: "August",
      september: "September",
      october: "October",
      november: "November",
      december: "December",
    };

    // Get all foreign key entities for validation and creation
    const [
      customers,
      mediums,
      channels,
      properties,
      variants,
      cities,
      companyCategories,
    ] = await Promise.all([
      db.Customer.findAll({
        where: { deletedAt: null },
        attributes: ["id", "name", "slug"],
      }),
      db.Medium.findAll({
        where: { deletedAt: null },
        attributes: ["id", "name", "slug"],
      }),
      db.Channel.findAll({
        where: { deletedAt: null },
        attributes: ["id", "name", "slug"],
      }),
      db.Property.findAll({
        where: { deletedAt: null },
        attributes: ["id", "name", "slug"],
      }),
      db.Variant.findAll({
        where: { deletedAt: null },
        attributes: ["id", "name", "slug"],
      }),
      db.City.findAll({
        where: { deletedAt: null },
        attributes: ["id", "name", "slug"],
      }),
      db.CompanyCategory.findAll({
        where: { deletedAt: null },
        attributes: ["id", "name", "slug"],
      }),
    ]);

    // Create lookup maps
    const customerMap = new Map(customers.map((c) => [c.slug, c]));
    const mediumMap = new Map(mediums.map((m) => [m.slug, m]));
    const channelMap = new Map(channels.map((c) => [c.slug, c]));
    const propertyMap = new Map(properties.map((p) => [p.slug, p]));
    const variantMap = new Map(variants.map((v) => [v.slug, v]));
    const cityMap = new Map(cities.map((c) => [c.slug, c]));
    const companyCategoryMap = new Map(
      companyCategories.map((cc) => [cc.slug, cc])
    );

    // Get all customer employees for validation
    const customerEmployees = await db.CustomerEmployee.findAll({
      where: { deletedAt: null },
      attributes: ["id", "name", "email", "phone", "customerId"],
    });

    // Create maps for customer employee lookup by email and phone
    const customerEmployeeEmailMap = new Map();
    const customerEmployeePhoneMap = new Map();
    customerEmployees.forEach((emp) => {
      if (emp.email) {
        customerEmployeeEmailMap.set(emp.email.toLowerCase(), emp);
      }
      if (emp.phone) {
        customerEmployeePhoneMap.set(emp.phone, emp);
      }
    });

    // Process each row
    for (let index = 0; index < rows.length; index++) {
      const row = rows[index];
      const rowNumber = index + 2; // Excel row number (1-indexed + header)

      try {
        // Extract contact information (exact column names)
        const contactName = (row["Contact Name"] || row.ContactName || "")
          .toString()
          .trim();

        const mobile = (row.Mobile || "").toString().trim();

        const emailId = (row["Email ID"] || row.EmailID || "")
          .toString()
          .trim();

        if (!contactName || (!mobile && !emailId)) {
          errorLog.push({
            row: rowNumber,
            data: row,
            reason:
              "Missing required fields: Contact Name and (Mobile or Email ID)",
          });
          continue;
        }

        // Step 1: Find or create customer employee
        let customerEmployeeId = null;
        let customerEmployee = null;

        // Check by email first, then by phone
        if (emailId) {
          customerEmployee = customerEmployeeEmailMap.get(
            emailId.toLowerCase()
          );
        }
        if (!customerEmployee && mobile) {
          customerEmployee = customerEmployeePhoneMap.get(mobile);
        }

        if (customerEmployee) {
          customerEmployeeId = customerEmployee.id;
        }

        // Step 2: Extract company information
        const companyName = (row["Company Name"] || row.CompanyName || "")
          .toString()
          .trim();

        if (!companyName) {
          errorLog.push({
            row: rowNumber,
            data: row,
            reason: "Missing required field: Company Name",
          });
          continue;
        }

        // Step 3: Find or create customer
        const customerSlug = utils.generateSlug(companyName);
        let customer = customerMap.get(customerSlug);
        let customerId = null;

        if (customer) {
          customerId = customer.id;
        } else {
          // Create new customer
          const address1 = (row.Address1 || "").toString().trim();
          const address2 = (row.Address2 || "").toString().trim();
          const address = [address1, address2].filter(Boolean).join(", ");
          const cityName = (row.City || "").toString().trim();

          console.log("City Name:", cityName);
          const pincode = (row.Pincode || "").toString().trim();
          const businessCategory = (
            row["Business Category"] ||
            row.BusinessCategory ||
            ""
          )
            .toString()
            .trim();

          // Find or create city
          let cityId = null;
          if (cityName) {
            console.log("City Name: inside if", cityName);
            const citySlug = utils.generateSlug(cityName);
            let city = cityMap.get(citySlug);

            if (city) {
              console.log("City found:", city);
              cityId = city.id;
            } else {
              console.log("City not found, creating new city");
              // Create new city
              try {
                const newCity = await db.City.create({
                  name: cityName,
                  slug: citySlug,
                  code: citySlug,
                  status: "active",
                  createdBy: userId || null,
                });
                console.log("New City created:", newCity);
                cityId = newCity.id;
                cityMap.set(citySlug, newCity);
              } catch (error) {
                errorLog.push({
                  row: rowNumber,
                  data: row,
                  reason: `Failed to create city: ${error.message}`,
                });
                continue;
              }
            }
          }

          // Find company category ID
          let companyCategoryId = null;
          if (businessCategory) {
            const categorySlug = utils.generateSlug(businessCategory);
            const category = companyCategoryMap.get(categorySlug);
            if (category) {
              companyCategoryId = category.id;
            }
          }

          try {
            const newCustomer = await db.Customer.create({
              name: companyName,
              slug: customerSlug,
              code: customerSlug,
              address: address || null,
              cityId: cityId,
              pincode: pincode || null,
              companyCategoryId: companyCategoryId,
              status: "active",
              createdBy: userId || null,
            });
            customerId = newCustomer.id;
            customerMap.set(customerSlug, newCustomer);
          } catch (error) {
            errorLog.push({
              row: rowNumber,
              data: row,
              reason: `Failed to create customer: ${error.message}`,
            });
            continue;
          }
        }

        // Step 4: Create customer employee if not found
        if (!customerEmployee && customerId) {
          // Extract designation for customer employee
          const designation = (row.Designation || "").toString().trim();

          try {
            const newCustomerEmployee = await db.CustomerEmployee.create({
              customerId: customerId,
              name: contactName,
              email: emailId || null,
              phone: mobile || null,
              designation: designation || null,
              status: "active",
              createdBy: userId || null,
            });
            customerEmployeeId = newCustomerEmployee.id;

            // Update maps for future lookups
            if (emailId) {
              customerEmployeeEmailMap.set(
                emailId.toLowerCase(),
                newCustomerEmployee
              );
            }
            if (mobile) {
              customerEmployeePhoneMap.set(mobile, newCustomerEmployee);
            }
          } catch (error) {
            errorLog.push({
              row: rowNumber,
              data: row,
              reason: `Failed to create customer employee: ${error.message}`,
            });
            continue;
          }
        }

        // Step 5: Extract and validate required entity names
        const mediumName = (row.Medium || "").toString().trim();
        const channelName = (row.Channel || "").toString().trim();
        const propertyName = (row.Property || "").toString().trim();

        if (!mediumName || !channelName || !propertyName) {
          errorLog.push({
            row: rowNumber,
            data: row,
            reason: "Missing required fields: Medium, Channel, Property",
          });
          continue;
        }

        // Step 6: Find or create Medium
        const mediumSlug = utils.generateSlug(mediumName);
        let medium = mediumMap.get(mediumSlug);
        let mediumId = null;

        if (medium) {
          mediumId = medium.id;
        } else {
          try {
            const newMedium = await db.Medium.create({
              name: mediumName,
              slug: mediumSlug,
              code: mediumSlug,
              status: "active",
              createdBy: userId || null,
            });
            mediumId = newMedium.id;
            mediumMap.set(mediumSlug, newMedium);
          } catch (error) {
            errorLog.push({
              row: rowNumber,
              data: row,
              reason: `Failed to create medium: ${error.message}`,
            });
            continue;
          }
        }

        // Step 7: Find or create Channel
        const channelSlug = utils.generateSlug(channelName);
        let channel = channelMap.get(channelSlug);
        let channelId = null;

        if (channel) {
          channelId = channel.id;
        } else {
          try {
            const newChannel = await db.Channel.create({
              mediumId: mediumId,
              name: channelName,
              slug: channelSlug,
              code: channelSlug,
              status: "active",
              createdBy: userId || null,
            });
            channelId = newChannel.id;
            channelMap.set(channelSlug, newChannel);
          } catch (error) {
            errorLog.push({
              row: rowNumber,
              data: row,
              reason: `Failed to create channel: ${error.message}`,
            });
            continue;
          }
        }

        // Step 8: Find or create Property
        const propertySlug = utils.generateSlug(propertyName);
        let property = propertyMap.get(propertySlug);
        let propertyId = null;

        if (property) {
          propertyId = property.id;
        } else {
          try {
            const newProperty = await db.Property.create({
              channelId: channelId,
              mediumId: mediumId,
              name: propertyName,
              slug: propertySlug,
              code: propertySlug,
              status: "active",
              createdBy: userId || null,
            });
            propertyId = newProperty.id;
            propertyMap.set(propertySlug, newProperty);
          } catch (error) {
            errorLog.push({
              row: rowNumber,
              data: row,
              reason: `Failed to create property: ${error.message}`,
            });
            continue;
          }
        }

        // Step 9: Find or create Variant (optional)
        let variantId = null;
        const variantName = (row.Variant || "").toString().trim();

        if (variantName) {
          const variantSlug = utils.generateSlug(variantName);
          let variant = variantMap.get(variantSlug);

          if (variant) {
            variantId = variant.id;
          } else {
            try {
              const newVariant = await db.Variant.create({
                propertyId: propertyId,
                name: variantName,
                slug: variantSlug,
                code: variantSlug,
                status: "active",
                createdBy: userId || null,
              });
              variantId = newVariant.id;
              variantMap.set(variantSlug, newVariant);
            } catch (error) {
              errorLog.push({
                row: rowNumber,
                data: row,
                reason: `Failed to create variant: ${error.message}`,
              });
              continue;
            }
          }
        }

        // Step 10: Extract transaction fields (exact column names)
        const quantityRaw = row.Quantity || "1";
        const quantity = parseInt(quantityRaw) || 1;

        const monthRaw = (row.Month || "").toString().trim();
        const month = monthRaw
          ? monthMapping[monthRaw.toLowerCase()] || monthRaw
          : null;

        const yearRaw = row.Year || new Date().getFullYear().toString();
        const year = parseInt(yearRaw) || new Date().getFullYear();

        const revenueRaw = row.Revenue || "0";
        const revenue = parseFloat(revenueRaw) || 0;

        const stage = (row.Stage || "").toString().trim();

        const notes = (row.Notes || "").toString().trim();

        // Step 11: Validate numeric fields
        if (isNaN(quantity) || isNaN(year) || isNaN(revenue)) {
          errorLog.push({
            row: rowNumber,
            data: row,
            reason: `Invalid numeric values - Quantity: ${quantityRaw}, Year: ${yearRaw}, Revenue: ${revenueRaw}`,
          });
          continue;
        }

        // Step 12: Prepare transaction data
        const transactionData = {
          customerId: customerId,
          customerEmployeeId: customerEmployeeId,
          mediumId: mediumId,
          channelId: channelId,
          propertyId: propertyId,
          variantId: variantId,
          quantity: quantity,
          month: month || null,
          year: year,
          revenue: revenue,
          stage: stage || null,
          notes: notes || null,
          status: "active",
          createdBy: userId || null,
        };

        insertList.push(transactionData);
      } catch (error) {
        errorLog.push({
          row: rowNumber,
          data: row,
          reason: `Processing error: ${error.message}`,
        });
      }
    }

    // Perform bulk insert using transaction
    let insertedTransactions = [];
    if (insertList.length > 0) {
      const transaction = await db.sequelize.transaction();
      try {
        insertedTransactions = await db.Transaction.bulkCreate(insertList, {
          transaction,
          validate: true,
        });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw new Error(`Bulk insert failed: ${error.message}`);
      }
    }

    // Create rejected rows file (skipped + errors) with reasons
    let rejectedFileUrl = null;
    const rejectedRows = [...skippedList, ...errorLog];

    if (rejectedRows.length > 0) {
      rejectedFileUrl = await createRejectedRowsFile(
        rows,
        rejectedRows,
        filePath
      );
    }

    // Clean up original uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Return standardized results for consistent frontend handling
    return {
      totalRows: rows.length,
      insertedCount: insertedTransactions.length,
      skippedCount: skippedList.length,
      errorCount: errorLog.length,
      insertedData: insertedTransactions,
      skippedData: skippedList,
      errors: errorLog,
      rejectedFileUrl: rejectedFileUrl,
    };
  } catch (error) {
    // Clean up uploaded file in case of error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw error;
  }
}

// Helper function to create rejected rows file
async function createRejectedRowsFile(
  originalRows,
  rejectedRows,
  originalFilePath
) {
  try {
    // Add reason column to rejected rows
    const rejectedRowsWithReasons = rejectedRows.map((rejected) => {
      const originalRow = originalRows[rejected.row - 1];
      return {
        ...originalRow,
        "Rejection Reason": rejected.reason,
      };
    });

    // Create new workbook with rejected rows
    const newWorkbook = xlsx.utils.book_new();
    const newWorksheet = xlsx.utils.json_to_sheet(rejectedRowsWithReasons);
    xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, "Rejected Rows");

    // Generate unique filename for rejected rows
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const originalFileName = path.basename(
      originalFilePath,
      path.extname(originalFilePath)
    );
    const rejectedFileName = `${originalFileName}_rejected_${timestamp}.xlsx`;
    const rejectedFilePath = path.join(
      path.dirname(originalFilePath),
      rejectedFileName
    );

    // Write the rejected rows file
    xlsx.writeFile(newWorkbook, rejectedFilePath);

    // Return download URL (adjust based on your file serving setup)
    return `/uploads/transactions/${path.basename(rejectedFilePath)}`;
  } catch (error) {
    console.error("Error creating rejected rows file:", error);
    return null;
  }
}

// Dashboard Analytics Functions

async function getDashboardSummary(filters = {}) {
  // For dashboard summary, we want default year behavior when no year is specified
  const whereClause = buildWhereClause(filters, true);

  // If multiple years are selected, sum revenue across all selected years
  // If no years are selected, default to current year behavior
  let currentYearRevenue, previousYearRevenue;

  if (
    filters.years &&
    Array.isArray(filters.years) &&
    filters.years.length > 0
  ) {
    // Sum revenue across all selected years
    currentYearRevenue = await db.Transaction.sum("revenue", {
      where: whereClause, // whereClause already includes year filter from buildWhereClause
    });

    // For trend calculation with multiple years, compare with the same years from previous period
    // Calculate previous period years (each selected year - 1)
    const selectedYears = filters.years.map((y) => parseInt(y));
    const previousPeriodYears = selectedYears.map((year) => year - 1);

    const previousWhereClause = { ...whereClause };
    previousWhereClause.year = { [Op.in]: previousPeriodYears };

    previousYearRevenue = await db.Transaction.sum("revenue", {
      where: previousWhereClause,
    });
  } else {
    // Default behavior: current year vs previous year
    const currentYear = filters.year || new Date().getFullYear();
    const previousYear = currentYear - 1;

    // Remove any existing year filter and set specific years
    const currentYearWhere = { ...whereClause };
    delete currentYearWhere.year;
    currentYearWhere.year = currentYear;

    const previousYearWhere = { ...whereClause };
    delete previousYearWhere.year;
    previousYearWhere.year = previousYear;

    currentYearRevenue = await db.Transaction.sum("revenue", {
      where: currentYearWhere,
    });

    previousYearRevenue = await db.Transaction.sum("revenue", {
      where: previousYearWhere,
    });
  }

  // Calculate trend percentage
  const trendPercentage =
    previousYearRevenue > 0
      ? ((currentYearRevenue - previousYearRevenue) / previousYearRevenue) * 100
      : 0;

  return {
    totalRevenue: currentYearRevenue || 0,
    previousYearRevenue: previousYearRevenue || 0,
    trendPercentage: parseFloat(trendPercentage.toFixed(1)),
  };
}

async function getRevenueOverTime(filters = {}) {
  // For revenue over time, we want default year behavior when no year is specified
  const whereClause = buildWhereClause(filters, true);

  // Handle multiple years or single year
  let queryWhere;
  if (
    filters.years &&
    Array.isArray(filters.years) &&
    filters.years.length > 0
  ) {
    // Multiple years - use the whereClause which already includes year filter
    queryWhere = whereClause;
  } else {
    // Single year - use specific year or default to current year
    const year = filters.year || new Date().getFullYear();
    queryWhere = { ...whereClause };
    delete queryWhere.year; // Remove any existing year filter
    queryWhere.year = year;
  }

  const revenueData = await db.Transaction.findAll({
    attributes: [
      "month",
      [Sequelize.fn("SUM", Sequelize.col("revenue")), "revenue"],
    ],
    where: queryWhere,
    group: ["month"],
    order: [
      [
        Sequelize.literal(
          "CASE month " +
            "WHEN 'January' THEN 1 " +
            "WHEN 'February' THEN 2 " +
            "WHEN 'March' THEN 3 " +
            "WHEN 'April' THEN 4 " +
            "WHEN 'May' THEN 5 " +
            "WHEN 'June' THEN 6 " +
            "WHEN 'July' THEN 7 " +
            "WHEN 'August' THEN 8 " +
            "WHEN 'September' THEN 9 " +
            "WHEN 'October' THEN 10 " +
            "WHEN 'November' THEN 11 " +
            "WHEN 'December' THEN 12 " +
            "END"
        ),
        "ASC",
      ],
    ],
    raw: true,
  });

  // Create array with all 12 months
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const fullMonthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const result = monthNames.map((monthName, index) => {
    const fullMonthName = fullMonthNames[index];
    const monthData = revenueData.find((item) => item.month === fullMonthName);
    return {
      month: monthName,
      revenue: monthData ? parseFloat(monthData.revenue) : 0,
    };
  });

  return result;
}

async function getYearOverYearComparison(filters = {}) {
  // Handle multiple years or single year comparison
  if (
    filters.years &&
    Array.isArray(filters.years) &&
    filters.years.length > 0
  ) {
    // For multiple years, compare the selected years with their previous years
    const selectedYears = filters.years.map((y) => parseInt(y));
    const previousYears = selectedYears.map((year) => year - 1);

    // Get current period data (selected years)
    const currentPeriodData = await getRevenueOverTime({
      ...filters,
      years: selectedYears.map((y) => y.toString()), // Keep as strings for consistency
    });

    // Get previous period data (previous years)
    const previousPeriodFilters = { ...filters };
    previousPeriodFilters.years = previousYears.map((y) => y.toString());
    const previousPeriodData = await getRevenueOverTime(previousPeriodFilters);

    // Combine the data
    const result = currentPeriodData.map((current, index) => ({
      month: current.month,
      currentYear: current.revenue,
      previousYear: previousPeriodData[index]?.revenue || 0,
    }));

    return result;
  } else {
    // Single year comparison (original behavior)
    const currentYear = filters.year || new Date().getFullYear();
    const previousYear = currentYear - 1;

    // Get current year data
    const currentYearData = await getRevenueOverTime({
      ...filters,
      year: currentYear,
    });

    // Get previous year data
    const previousYearData = await getRevenueOverTime({
      ...filters,
      year: previousYear,
    });

    // Combine the data
    const result = currentYearData.map((current, index) => ({
      month: current.month,
      currentYear: current.revenue,
      previousYear: previousYearData[index]?.revenue || 0,
    }));

    return result;
  }
}

async function getRevenueByCategory(filters = {}) {
  // For revenue by category dashboard, use default year when no year filter is specified
  const whereClause = buildWhereClause(filters, true);
  const categoryType = filters.categoryType || "medium"; // medium, channel, property, variant

  let includeModel, asAlias;

  switch (categoryType) {
    case "medium":
      includeModel = db.Medium;
      asAlias = "mediumInfo";
      break;
    case "channel":
      includeModel = db.Channel;
      asAlias = "channelInfo";
      break;
    case "property":
      includeModel = db.Property;
      asAlias = "propertyInfo";
      break;
    case "variant":
      includeModel = db.Variant;
      asAlias = "variantInfo";
      break;
    default:
      includeModel = db.Medium;
      asAlias = "mediumInfo";
  }

  const revenueData = await db.Transaction.findAll({
    attributes: [
      [Sequelize.col(`${asAlias}.name`), "category"],
      [Sequelize.fn("SUM", Sequelize.col("revenue")), "revenue"],
    ],
    include: [
      {
        model: includeModel,
        as: asAlias,
        attributes: [],
        required: true,
      },
    ],
    where: whereClause,
    group: [Sequelize.col(`${asAlias}.name`)],
    order: [[Sequelize.fn("SUM", Sequelize.col("revenue")), "DESC"]],
    raw: true,
  });

  return revenueData.map((item) => ({
    variant: item.category,
    revenue: parseFloat(item.revenue),
  }));
}

async function getTopClients(filters = {}) {
  // For top clients dashboard, use default year when no year filter is specified
  const whereClause = buildWhereClause(filters, true);
  const limit = parseInt(filters.limit) || 5;

  // Get current period revenue by customer
  const currentPeriodRevenue = await db.Transaction.findAll({
    attributes: [
      [Sequelize.col("customerInfo.name"), "company"],
      [Sequelize.fn("SUM", Sequelize.col("revenue")), "revenue"],
    ],
    include: [
      {
        model: db.Customer,
        as: "customerInfo",
        attributes: [],
        required: true,
      },
    ],
    where: whereClause,
    group: [
      Sequelize.col("customerInfo.id"),
      Sequelize.col("customerInfo.name"),
    ],
    order: [[Sequelize.fn("SUM", Sequelize.col("revenue")), "DESC"]],
    limit: limit,
    raw: true,
  });

  // Calculate trends (comparing with previous period)
  const result = await Promise.all(
    currentPeriodRevenue.map(async (client) => {
      // Get previous period revenue for trend calculation
      const previousPeriodWhere = buildPreviousPeriodWhereClause(filters);

      const previousRevenue = await db.Transaction.sum("revenue", {
        include: [
          {
            model: db.Customer,
            as: "customerInfo",
            where: { name: client.company },
            required: true,
          },
        ],
        where: previousPeriodWhere,
      });

      const currentRevenue = parseFloat(client.revenue);
      const trend =
        previousRevenue > 0
          ? ((currentRevenue - previousRevenue) / previousRevenue) * 100
          : 0;

      return {
        company: client.company,
        revenue: currentRevenue,
        trend: parseFloat(trend.toFixed(1)),
      };
    })
  );

  return result;
}

async function getFilterOptions() {
  // Get unique years from transactions
  const years = await db.Transaction.findAll({
    attributes: [[Sequelize.fn("DISTINCT", Sequelize.col("year")), "year"]],
    where: {
      deletedAt: null,
      year: { [Op.ne]: null },
    },
    order: [["year", "DESC"]],
    raw: true,
  });

  // Get unique stages from transactions
  // const stages = await db.Transaction.findAll({
  //   attributes: [[Sequelize.fn("DISTINCT", Sequelize.col("stage")), "stage"]],
  //   where: {
  //     deletedAt: null,
  //     stage: { [Op.ne]: null }, // Exclude null stages
  //   },
  //   order: [["stage", "ASC"]],
  //   raw: true,
  // });

  // Get active options for dropdowns
  const [mediums, channels, properties, variants, cities, customers, stages] =
    await Promise.all([
      db.Medium.findAll({
        where: { deletedAt: null, status: "active" },
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      }),
      db.Channel.findAll({
        where: { deletedAt: null, status: "active" },
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      }),
      db.Property.findAll({
        where: { deletedAt: null, status: "active" },
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      }),
      db.Variant.findAll({
        where: { deletedAt: null, status: "active" },
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      }),
      db.City.findAll({
        where: { deletedAt: null, status: "active" },
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      }),
      db.Customer.findAll({
        where: { deletedAt: null, status: "active" },
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      }),
      db.Stage.findAll({
        where: { deletedAt: null, status: "active" },
        attributes: ["id", "name"],
        order: [["name", "ASC"]],
      }),
    ]);

  const monthOptions = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  return {
    years: years.map((y) => y.year.toString()),
    months: monthOptions,
    mediums: mediums.map((m) => m.name),
    channels: channels.map((c) => c.name),
    properties: properties.map((p) => p.name),
    variants: variants.map((v) => v.name),
    cities: cities.map((c) => c.name),
    customers: customers.map((c) => c.name),
    stages: stages.map((s) => s.name), // Filter out any null/empty stages
    // Add contact status options to match frontend expectations
    contactStatus: ["Active", "Inactive", "Pending"],
    // Add contact category options
    contactCategories: ["Solution Provider", "Brand", "Agency", "Consultant"],
  };
}

// Consolidated Dashboard API
async function getAllDashboardData(filters = {}) {
  try {
    // Process filters to handle arrays and convert names to IDs
    const processedFilters = await processFilters(filters);

    // Get all dashboard data in parallel for better performance
    const [
      summary,
      revenueOverTime,
      yearComparison,
      revenueByCategory,
      topClients,
      filterOptions,
    ] = await Promise.all([
      getDashboardSummary(processedFilters),
      getRevenueOverTime(processedFilters),
      getYearOverYearComparison(processedFilters),
      getRevenueByCategory(processedFilters),
      getTopClients(processedFilters),
      getFilterOptions(),
    ]);

    return {
      summary,
      revenueOverTime,
      yearComparison,
      revenueByVariant: revenueByCategory,
      topClients,
      filterOptions,
    };
  } catch (error) {
    console.error("Error in getAllDashboardData:", error);
    throw error;
  }
}

// Helper function to process filters and convert names to IDs
async function processFilters(filters) {
  const processedFilters = { ...filters };

  // Handle frontend filter parameter names and convert to arrays if needed
  const filterMappings = [
    { frontend: "yearFilters", backend: "years" },
    { frontend: "monthFilters", backend: "months" },
    { frontend: "mediumFilters", backend: "mediums" },
    { frontend: "channelFilters", backend: "channels" },
    { frontend: "variantFilters", backend: "variants" }, // Note: Frontend calls it variant but may use properties
    { frontend: "cityFilters", backend: "cities" },
    { frontend: "contactFilters", backend: "customers" },
    { frontend: "contactStatusFilters", backend: "contactStatus" },
    { frontend: "stageFilters", backend: "stages" },
  ];

  // Convert frontend filter names to backend names
  filterMappings.forEach(({ frontend, backend }) => {
    if (filters[frontend]) {
      processedFilters[backend] = Array.isArray(filters[frontend])
        ? filters[frontend]
        : [filters[frontend]];
      delete processedFilters[frontend];
    }
  });

  // Convert filter arrays to ID arrays for database queries
  if (
    processedFilters.mediums &&
    Array.isArray(processedFilters.mediums) &&
    processedFilters.mediums.length > 0
  ) {
    const mediums = await db.Medium.findAll({
      where: { name: { [Op.in]: processedFilters.mediums }, deletedAt: null },
      attributes: ["id"],
    });
    processedFilters.mediumIds = mediums.map((m) => m.id);
    delete processedFilters.mediums;
  }

  if (
    processedFilters.channels &&
    Array.isArray(processedFilters.channels) &&
    processedFilters.channels.length > 0
  ) {
    const channels = await db.Channel.findAll({
      where: { name: { [Op.in]: processedFilters.channels }, deletedAt: null },
      attributes: ["id"],
    });
    processedFilters.channelIds = channels.map((c) => c.id);
    delete processedFilters.channels;
  }

  // Handle variants (which might actually be properties based on frontend code)
  if (
    processedFilters.variants &&
    Array.isArray(processedFilters.variants) &&
    processedFilters.variants.length > 0
  ) {
    // Try both variants and properties tables
    const [variants, properties] = await Promise.all([
      db.Variant.findAll({
        where: {
          name: { [Op.in]: processedFilters.variants },
          deletedAt: null,
        },
        attributes: ["id"],
      }),
      db.Property.findAll({
        where: {
          name: { [Op.in]: processedFilters.variants },
          deletedAt: null,
        },
        attributes: ["id"],
      }),
    ]);

    if (variants.length > 0) {
      processedFilters.variantIds = variants.map((v) => v.id);
    }
    if (properties.length > 0) {
      processedFilters.propertyIds = properties.map((p) => p.id);
    }
    delete processedFilters.variants;
  }

  // Handle properties separately
  if (
    processedFilters.properties &&
    Array.isArray(processedFilters.properties) &&
    processedFilters.properties.length > 0
  ) {
    const properties = await db.Property.findAll({
      where: {
        name: { [Op.in]: processedFilters.properties },
        deletedAt: null,
      },
      attributes: ["id"],
    });
    processedFilters.propertyIds = properties.map((p) => p.id);
    delete processedFilters.properties;
  }

  if (
    processedFilters.cities &&
    Array.isArray(processedFilters.cities) &&
    processedFilters.cities.length > 0
  ) {
    const cities = await db.City.findAll({
      where: { name: { [Op.in]: processedFilters.cities }, deletedAt: null },
      attributes: ["id"],
    });
    processedFilters.cityIds = cities.map((c) => c.id);
    delete processedFilters.cities;
  }

  if (
    processedFilters.customers &&
    Array.isArray(processedFilters.customers) &&
    processedFilters.customers.length > 0
  ) {
    const customers = await db.Customer.findAll({
      where: { name: { [Op.in]: processedFilters.customers }, deletedAt: null },
      attributes: ["id"],
    });
    processedFilters.customerIds = customers.map((c) => c.id);
    delete processedFilters.customers;
  }

  // Handle contact status (this might need to be mapped to customer status or a separate field)
  if (
    processedFilters.contactStatus &&
    Array.isArray(processedFilters.contactStatus) &&
    processedFilters.contactStatus.length > 0
  ) {
    // Map contact status to customer status or handle as needed
    processedFilters.customerStatus = processedFilters.contactStatus;
    delete processedFilters.contactStatus;
  }

  // Handle stage filters (stages are stored as strings, no ID conversion needed)
  if (
    processedFilters.stages &&
    Array.isArray(processedFilters.stages) &&
    processedFilters.stages.length > 0
  ) {
    // Keep stages as is since they are string values
    processedFilters.stageFilters = processedFilters.stages;
    delete processedFilters.stages;
  }

  return processedFilters;
}

// Helper functions for dashboard analytics
function buildWhereClause(filters, useDefaultYear = false) {
  const whereClause = { deletedAt: null, status: "active" };

  // Handle year and month filters
  if (
    filters.years &&
    Array.isArray(filters.years) &&
    filters.years.length > 0
  ) {
    // For multiple years, filter by year column
    whereClause.year = { [Op.in]: filters.years.map((y) => parseInt(y)) };
  } else if (filters.year) {
    whereClause.year = parseInt(filters.year);
  } else if (useDefaultYear) {
    // Only apply default year when explicitly requested (for dashboard functions)
    whereClause.year = new Date().getFullYear();
  }
  // If no year filter and useDefaultYear is false, return data from ALL years

  // Handle month filter
  if (
    filters.months &&
    Array.isArray(filters.months) &&
    filters.months.length > 0
  ) {
    // Map short month names to full month names
    const monthMapping = {
      Jan: "January",
      Feb: "February",
      Mar: "March",
      Apr: "April",
      May: "May",
      Jun: "June",
      Jul: "July",
      Aug: "August",
      Sep: "September",
      Oct: "October",
      Nov: "November",
      Dec: "December",
    };

    const fullMonthNames = filters.months
      .map((month) => monthMapping[month])
      .filter(Boolean);
    if (fullMonthNames.length > 0) {
      whereClause.month = { [Op.in]: fullMonthNames };
    }
  } else if (filters.month) {
    // Map short month name to full month name
    const monthMapping = {
      Jan: "January",
      Feb: "February",
      Mar: "March",
      Apr: "April",
      May: "May",
      Jun: "June",
      Jul: "July",
      Aug: "August",
      Sep: "September",
      Oct: "October",
      Nov: "November",
      Dec: "December",
    };

    const fullMonthName = monthMapping[filters.month];
    if (fullMonthName) {
      whereClause.month = fullMonthName;
    }
  }

  // Handle ID-based filters (support both single values and arrays)
  if (
    filters.customerIds &&
    Array.isArray(filters.customerIds) &&
    filters.customerIds.length > 0
  ) {
    whereClause.customerId = { [Op.in]: filters.customerIds };
  } else if (filters.customerId) {
    whereClause.customerId = parseInt(filters.customerId);
  }

  if (
    filters.mediumIds &&
    Array.isArray(filters.mediumIds) &&
    filters.mediumIds.length > 0
  ) {
    whereClause.mediumId = { [Op.in]: filters.mediumIds };
  } else if (filters.mediumId) {
    whereClause.mediumId = parseInt(filters.mediumId);
  }

  if (
    filters.channelIds &&
    Array.isArray(filters.channelIds) &&
    filters.channelIds.length > 0
  ) {
    whereClause.channelId = { [Op.in]: filters.channelIds };
  } else if (filters.channelId) {
    whereClause.channelId = parseInt(filters.channelId);
  }

  if (
    filters.propertyIds &&
    Array.isArray(filters.propertyIds) &&
    filters.propertyIds.length > 0
  ) {
    whereClause.propertyId = { [Op.in]: filters.propertyIds };
  } else if (filters.propertyId) {
    whereClause.propertyId = parseInt(filters.propertyId);
  }

  if (
    filters.variantIds &&
    Array.isArray(filters.variantIds) &&
    filters.variantIds.length > 0
  ) {
    whereClause.variantId = { [Op.in]: filters.variantIds };
  } else if (filters.variantId) {
    whereClause.variantId = parseInt(filters.variantId);
  }

  if (
    filters.cityIds &&
    Array.isArray(filters.cityIds) &&
    filters.cityIds.length > 0
  ) {
    whereClause.cityId = { [Op.in]: filters.cityIds };
  } else if (filters.cityId) {
    whereClause.cityId = parseInt(filters.cityId);
  }

  // Handle stage filters (stages are stored as strings)
  if (
    filters.stageFilters &&
    Array.isArray(filters.stageFilters) &&
    filters.stageFilters.length > 0
  ) {
    whereClause.stage = { [Op.in]: filters.stageFilters };
  } else if (filters.stage) {
    whereClause.stage = filters.stage;
  }

  return whereClause;
}

function buildPreviousPeriodWhereClause(filters) {
  const whereClause = { deletedAt: null, status: "active" };

  // Handle multiple years or single year for previous period
  if (
    filters.years &&
    Array.isArray(filters.years) &&
    filters.years.length > 0
  ) {
    // For multiple years, calculate previous period years
    const selectedYears = filters.years.map((y) => parseInt(y));
    const previousPeriodYears = selectedYears.map((year) => year - 1);
    whereClause.year = { [Op.in]: previousPeriodYears };
  } else if (filters.year) {
    const previousYear = parseInt(filters.year) - 1;
    whereClause.year = previousYear;
  } else {
    // Default to previous year
    const currentYear = new Date().getFullYear();
    const previousYear = currentYear - 1;
    whereClause.year = previousYear;
  }

  // Apply other filters (excluding year-related ones)
  const otherFilters = { ...filters };
  delete otherFilters.years;
  delete otherFilters.year;

  // Build where clause for other filters (don't use default year for other filters)
  const baseWhereClause = buildWhereClause(otherFilters, false);

  // Merge with year-specific where clause (but exclude the year field from baseWhereClause)
  const { year: _, ...otherConditions } = baseWhereClause;
  Object.assign(whereClause, otherConditions);

  return whereClause;
}

// Reports API Function
async function generateTransactionReport(filters = {}) {
  try {
    // Process filters to handle arrays and convert names to IDs
    const processedFilters = await processFilters(filters);

    // Handle custom date range for months and years
    const whereClause = buildReportWhereClause(processedFilters);

    // Get filtered transactions with all related entity information
    const transactions = await db.Transaction.findAll({
      where: whereClause,
      include: [
        {
          model: db.Customer,
          as: "customerInfo",
          attributes: ["id", "name", "code", "email"],
          required: false,
        },
        {
          model: db.Medium,
          as: "mediumInfo",
          attributes: ["id", "name", "code"],
          required: false,
        },
        {
          model: db.Channel,
          as: "channelInfo",
          attributes: ["id", "name", "code"],
          required: false,
        },
        {
          model: db.Property,
          as: "propertyInfo",
          attributes: ["id", "name", "code"],
          required: false,
        },
        {
          model: db.Variant,
          as: "variantInfo",
          attributes: ["id", "name", "code"],
          required: false,
        },
        {
          model: db.City,
          as: "cityInfo",
          attributes: ["id", "name", "code", "state"],
          required: false,
        },
      ],
      order: [
        ["year", "DESC"],
        ["month", "ASC"],
        ["createdAt", "DESC"],
      ],
    });

    // Generate Excel file with filtered data
    const excelFileUrl = await createTransactionReportExcel(
      transactions,
      filters
    );

    return {
      success: true,
      message: "Transaction report generated successfully",
      downloadUrl: excelFileUrl,
      totalRecords: transactions.length,
      totalRevenue: transactions.reduce(
        (sum, t) => sum + (parseFloat(t.revenue) || 0),
        0
      ),
    };
  } catch (error) {
    console.error("Error generating transaction report:", error);
    throw error;
  }
}

// Helper function to build where clause for reports with custom date range support
function buildReportWhereClause(filters) {
  const whereClause = { deletedAt: null, status: "active" };

  // Handle custom date range for months and years
  if (
    filters.startMonth &&
    filters.startYear &&
    filters.endMonth &&
    filters.endYear
  ) {
    // Custom date range logic
    const startYear = parseInt(filters.startYear);
    const endYear = parseInt(filters.endYear);
    const startMonth = filters.startMonth;
    const endMonth = filters.endMonth;

    if (startYear === endYear) {
      // Same year - filter by month range
      whereClause.year = startYear;
      whereClause.month = {
        [Op.between]: [startMonth, endMonth],
      };
    } else {
      // Different years - complex date range
      whereClause[Op.or] = [
        // Start year: from start month to December
        {
          year: startYear,
          month: {
            [Op.gte]: startMonth,
          },
        },
        // Middle years: all months
        ...(endYear - startYear > 1
          ? [
              {
                year: {
                  [Op.between]: [startYear + 1, endYear - 1],
                },
              },
            ]
          : []),
        // End year: from January to end month
        {
          year: endYear,
          month: {
            [Op.lte]: endMonth,
          },
        },
      ];
    }
  } else {
    // Use existing year/month filter logic
    if (
      filters.years &&
      Array.isArray(filters.years) &&
      filters.years.length > 0
    ) {
      whereClause.year = { [Op.in]: filters.years.map((y) => parseInt(y)) };
    } else if (filters.year) {
      whereClause.year = parseInt(filters.year);
    }

    if (
      filters.months &&
      Array.isArray(filters.months) &&
      filters.months.length > 0
    ) {
      whereClause.month = { [Op.in]: filters.months };
    } else if (filters.month) {
      whereClause.month = filters.month;
    }
  }

  // Apply other filters using existing logic
  if (
    filters.mediumIds &&
    Array.isArray(filters.mediumIds) &&
    filters.mediumIds.length > 0
  ) {
    whereClause.mediumId = { [Op.in]: filters.mediumIds };
  } else if (filters.mediumId) {
    whereClause.mediumId = parseInt(filters.mediumId);
  }

  if (
    filters.channelIds &&
    Array.isArray(filters.channelIds) &&
    filters.channelIds.length > 0
  ) {
    whereClause.channelId = { [Op.in]: filters.channelIds };
  } else if (filters.channelId) {
    whereClause.channelId = parseInt(filters.channelId);
  }

  if (
    filters.propertyIds &&
    Array.isArray(filters.propertyIds) &&
    filters.propertyIds.length > 0
  ) {
    whereClause.propertyId = { [Op.in]: filters.propertyIds };
  } else if (filters.propertyId) {
    whereClause.propertyId = parseInt(filters.propertyId);
  }

  if (
    filters.variantIds &&
    Array.isArray(filters.variantIds) &&
    filters.variantIds.length > 0
  ) {
    whereClause.variantId = { [Op.in]: filters.variantIds };
  } else if (filters.variantId) {
    whereClause.variantId = parseInt(filters.variantId);
  }

  if (
    filters.cityIds &&
    Array.isArray(filters.cityIds) &&
    filters.cityIds.length > 0
  ) {
    whereClause.cityId = { [Op.in]: filters.cityIds };
  } else if (filters.cityId) {
    whereClause.cityId = parseInt(filters.cityId);
  }

  if (
    filters.customerIds &&
    Array.isArray(filters.customerIds) &&
    filters.customerIds.length > 0
  ) {
    whereClause.customerId = { [Op.in]: filters.customerIds };
  } else if (filters.customerId) {
    whereClause.customerId = parseInt(filters.customerId);
  }

  if (
    filters.stageFilters &&
    Array.isArray(filters.stageFilters) &&
    filters.stageFilters.length > 0
  ) {
    whereClause.stage = { [Op.in]: filters.stageFilters };
  } else if (filters.stage) {
    whereClause.stage = filters.stage;
  }

  return whereClause;
}

// Helper function to create Excel file for transaction report
async function createTransactionReportExcel(transactions, filters) {
  try {
    // Create workbook and worksheet
    const workbook = xlsx.utils.book_new();

    // Prepare filter information for display at the top
    const filterInfo = [];
    filterInfo.push(["Transaction Report"]);
    filterInfo.push(["Generated on:", new Date().toLocaleString()]);
    filterInfo.push([""]);
    filterInfo.push(["Applied Filters:"]);

    // Add filter details in user-friendly format
    if (
      filters.startMonth &&
      filters.startYear &&
      filters.endMonth &&
      filters.endYear
    ) {
      filterInfo.push([
        "Date Range:",
        `${filters.startMonth} ${filters.startYear} to ${filters.endMonth} ${filters.endYear}`,
      ]);
    } else {
      if (filters.years && filters.years.length > 0) {
        filterInfo.push(["Years:", filters.years.join(", ")]);
      }
      if (filters.months && filters.months.length > 0) {
        filterInfo.push(["Months:", filters.months.join(", ")]);
      }
    }

    if (filters.mediums && filters.mediums.length > 0) {
      filterInfo.push(["Mediums:", filters.mediums.join(", ")]);
    }
    if (filters.channels && filters.channels.length > 0) {
      filterInfo.push(["Channels:", filters.channels.join(", ")]);
    }
    if (filters.properties && filters.properties.length > 0) {
      filterInfo.push(["Properties:", filters.properties.join(", ")]);
    }
    if (filters.variants && filters.variants.length > 0) {
      filterInfo.push(["Variants:", filters.variants.join(", ")]);
    }
    if (filters.cities && filters.cities.length > 0) {
      filterInfo.push(["Cities:", filters.cities.join(", ")]);
    }
    if (filters.customers && filters.customers.length > 0) {
      filterInfo.push(["Customers:", filters.customers.join(", ")]);
    }
    if (filters.stages && filters.stages.length > 0) {
      filterInfo.push(["Stages:", filters.stages.join(", ")]);
    }

    filterInfo.push([""]);
    filterInfo.push([""]);

    // Prepare transaction data
    const headers = [
      "Company Name",
      "Medium Name",
      "Channel Name",
      "Property Name",
      "Variant Name",
      "City Name",
      "Month",
      "Year",
      "Revenue",
    ];

    const transactionData = transactions.map((transaction) => [
      transaction.customerInfo?.name || "N/A",
      transaction.mediumInfo?.name || "N/A",
      transaction.channelInfo?.name || "N/A",
      transaction.propertyInfo?.name || "N/A",
      transaction.variantInfo?.name || "N/A",
      transaction.cityInfo?.name || "N/A",
      transaction.month || "N/A",
      transaction.year || "N/A",
      parseFloat(transaction.revenue) || 0,
    ]);

    // Calculate total revenue
    const totalRevenue = transactions.reduce(
      (sum, t) => sum + (parseFloat(t.revenue) || 0),
      0
    );

    // Combine all data
    const allData = [
      ...filterInfo,
      headers,
      ...transactionData,
      ["", "", "", "", "", "", "", "Total Revenue:", totalRevenue],
    ];

    // Create worksheet from data
    const worksheet = xlsx.utils.aoa_to_sheet(allData);

    // Add some basic styling by setting column widths
    const columnWidths = [
      { wch: 20 }, // Customer Name
      { wch: 15 }, // Medium Name
      { wch: 15 }, // Channel Name
      { wch: 15 }, // Property Name
      { wch: 15 }, // Variant Name
      { wch: 15 }, // City Name
      { wch: 12 }, // Month
      { wch: 8 }, // Year
      { wch: 12 }, // Revenue
    ];
    worksheet["!cols"] = columnWidths;

    // Add worksheet to workbook
    xlsx.utils.book_append_sheet(workbook, worksheet, "Transaction Report");

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `transaction-report-${timestamp}.xlsx`;
    const uploadsDir = path.join(__basedir, "uploads", "reports");

    // Ensure reports directory exists
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const filePath = path.join(uploadsDir, filename);

    // Write Excel file
    xlsx.writeFile(workbook, filePath);

    // Return download URL with /api prefix
    const relativePath = path.relative(__basedir + "uploads", filePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating transaction report Excel:", error);
    throw error;
  }
}

// Stage-based Customer Employee Report API Function
async function generateStageCustomerReport(filters = {}) {
  try {
    // Build where clause for stage filtering
    const whereClause = { deletedAt: null, status: "active" };

    // Handle stages filter
    if (
      filters.stages &&
      Array.isArray(filters.stages) &&
      filters.stages.length > 0
    ) {
      whereClause.stage = { [Op.in]: filters.stages };
    } else if (filters.stage) {
      whereClause.stage = filters.stage;
    }

    // Get filtered transactions with customer and customer employee information
    const transactions = await db.Transaction.findAll({
      where: whereClause,
      include: [
        {
          model: db.Customer,
          as: "customerInfo",
          attributes: ["id", "name", "email", "phone", "address"],
          required: false,
        },
        {
          model: db.CustomerEmployee,
          as: "customerEmployeeInfo",
          attributes: [
            "id",
            "name",
            "email",
            "phone",
            "designation",
            "department",
          ],
          required: false,
        },
      ],
      order: [
        ["stage", "ASC"],
        ["createdAt", "DESC"],
      ],
    });

    // Generate Excel file with customer and employee data
    const excelFileUrl = await createStageCustomerReportExcel(
      transactions,
      filters
    );

    return {
      success: true,
      message: "Stage customer report generated successfully",
      downloadUrl: excelFileUrl,
      totalRecords: transactions.length,
      stages: filters.stages || [filters.stage].filter(Boolean),
    };
  } catch (error) {
    console.error("Error generating stage customer report:", error);
    throw error;
  }
}

// Helper function to create Excel file for stage customer report
async function createStageCustomerReportExcel(transactions, filters) {
  try {
    // Create workbook and worksheet
    const workbook = xlsx.utils.book_new();

    // Prepare filter information for display at the top
    const filterInfo = [];
    filterInfo.push(["Stage Customer Report"]);
    filterInfo.push(["Generated on:", new Date().toLocaleString()]);
    filterInfo.push([""]);
    filterInfo.push(["Applied Filters:"]);

    // Add stage filter details
    if (filters.stages && filters.stages.length > 0) {
      filterInfo.push(["Stages:", filters.stages.join(", ")]);
    } else if (filters.stage) {
      filterInfo.push(["Stage:", filters.stage]);
    } else {
      filterInfo.push(["Stages:", "All Stages"]);
    }

    filterInfo.push([""]);
    filterInfo.push([""]);

    // Prepare transaction data with customer and employee information
    const headers = [
      "Stage",
      "Company Name",
      "Company Email",
      "Company Phone",
      "Company Address",
      "Name",
      "Email",
      "Phone",
      "Designation",
      "Department",
    ];

    const transactionData = transactions.map((transaction) => [
      transaction.stage || "N/A",
      transaction.customerInfo?.name || "N/A",
      transaction.customerInfo?.email || "N/A",
      transaction.customerInfo?.phone || "N/A",
      transaction.customerInfo?.address || "N/A",
      transaction.customerEmployeeInfo?.name || "N/A",
      transaction.customerEmployeeInfo?.email || "N/A",
      transaction.customerEmployeeInfo?.phone || "N/A",
      transaction.customerEmployeeInfo?.designation || "N/A",
      transaction.customerEmployeeInfo?.department || "N/A",
    ]);

    // Combine all data
    const allData = [...filterInfo, headers, ...transactionData];

    // Create worksheet from data
    const worksheet = xlsx.utils.aoa_to_sheet(allData);

    // Add some basic styling by setting column widths
    const columnWidths = [
      { wch: 15 }, // Stage
      { wch: 20 }, // Company Name
      { wch: 25 }, // Company Email
      { wch: 15 }, // Company Phone
      { wch: 30 }, // Company Address
      { wch: 20 }, // Employee Name
      { wch: 25 }, // Employee Email
      { wch: 15 }, // Employee Phone
      { wch: 20 }, // Employee Designation
      { wch: 18 }, // Employee Department
    ];
    worksheet["!cols"] = columnWidths;

    // Add worksheet to workbook
    xlsx.utils.book_append_sheet(workbook, worksheet, "Stage Customer Report");

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `stage-customer-report-${timestamp}.xlsx`;
    const uploadsDir = path.join(__basedir, "uploads", "reports");

    // Ensure reports directory exists
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const filePath = path.join(uploadsDir, filename);

    // Write Excel file
    xlsx.writeFile(workbook, filePath);

    // Return download URL with /api prefix
    const relativePath = path.relative(__basedir + "uploads", filePath);
    return `/uploads/${relativePath.replace(/\\/g, "/")}`;
  } catch (error) {
    console.error("Error creating stage customer report Excel:", error);
    throw error;
  }
}
